import os
import re
import requests
from functools import lru_cache

class APIConfig:
    def __init__(self):
        # These environment variables will be populated from GitHub secrets
        # during CI/CD workflows or deployment
        self.openai_api_key = os.environ.get('OPENAI_API_KEY')
        self.ai_studio_api_key = os.environ.get('AI_STUDIO_API_KEY')
        self.stability_api_key = os.environ.get('STABILITY_API_KEY')
        self.elevenlabs_api_key = os.environ.get('ELEVENLABS_API_KEY')

    def validate(self, raise_error=True):
        missing_keys = []

        if not self.openai_api_key:
            missing_keys.append("OPENAI_API_KEY")
        if not self.ai_studio_api_key:
            missing_keys.append("AI_STUDIO_API_KEY")
        if not self.stability_api_key:
            missing_keys.append("STABILITY_API_KEY")
        if not self.elevenlabs_api_key:
            missing_keys.append("ELEVENLABS_API_KEY")

        if missing_keys and raise_error:
            raise ValueError(f"The following API keys are missing: {', '.join(missing_keys)}")

        return len(missing_keys) == 0

    def validate_key(self, key_name, key_value):
        """Validate a specific API key"""
        if key_name == 'openai_api_key':
            return self.validate_openai_key(key_value)
        elif key_name == 'ai_studio_api_key':
            return self.validate_ai_studio_key(key_value)
        elif key_name == 'stability_api_key':
            return self.validate_stability_key(key_value)
        elif key_name == 'elevenlabs_api_key':
            return self.validate_elevenlabs_key(key_value)
        else:
            return False

    def validate_openai_key(self, key_value):
        """Validate an OpenAI API key by making a test request"""
        if not key_value or not key_value.startswith('sk-'):
            return False

        try:
            headers = {
                'Authorization': f'Bearer {key_value}',
                'Content-Type': 'application/json'
            }
            response = requests.get('https://api.openai.com/v1/models', headers=headers, timeout=5)
            return response.status_code == 200
        except Exception:
            return False

    def validate_ai_studio_key(self, key_value):
        """Validate an AI Studio API key"""
        # This is a placeholder implementation
        # In a real application, you would make a test request to the AI Studio API
        if not key_value or len(key_value) < 20:
            return False

        # Check if it matches the expected format (example pattern)
        pattern = r'^[A-Za-z0-9_-]{20,}$'
        return bool(re.match(pattern, key_value))

    def validate_stability_key(self, key_value):
        """Validate a Stability API key by making a test request"""
        if not key_value or len(key_value) < 20:
            return False

        try:
            headers = {
                'Authorization': f'Bearer {key_value}',
                'Content-Type': 'application/json'
            }
            response = requests.get('https://api.stability.ai/v1/engines/list', headers=headers, timeout=5)
            return response.status_code == 200
        except Exception:
            return False

    def validate_elevenlabs_key(self, key_value):
        """Validate an ElevenLabs API key by making a test request"""
        if not key_value or len(key_value) < 20:
            return False

        try:
            headers = {
                'xi-api-key': key_value,
                'Content-Type': 'application/json'
            }
            response = requests.get('https://api.elevenlabs.io/v1/voices', headers=headers, timeout=5)
            return response.status_code == 200
        except Exception:
            return False

@lru_cache()
def get_api_config():
    config = APIConfig()
    # Only validate in production or when explicitly requested
    # In development, allow missing keys and handle gracefully
    import os
    if os.environ.get('DJANGO_DEBUG', 'True').lower() != 'true':
        config.validate()
    return config
