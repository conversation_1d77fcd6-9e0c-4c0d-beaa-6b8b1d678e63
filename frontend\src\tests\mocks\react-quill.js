/**
 * Mock for react-quill to handle JSDOM limitations
 * This mock provides a simplified version of ReactQuill that works in test environments
 */

import React, { forwardRef, useImperativeHandle, useState, useEffect } from 'react';

// Mock Quill toolbar configuration
const mockToolbarConfig = [
  ['bold', 'italic', 'underline', 'strike'],
  ['blockquote', 'code-block'],
  [{ 'header': 1 }, { 'header': 2 }],
  [{ 'list': 'ordered' }, { 'list': 'bullet' }],
  [{ 'script': 'sub' }, { 'script': 'super' }],
  [{ 'indent': '-1' }, { 'indent': '+1' }],
  [{ 'color': [] }, { 'background': [] }],
  ['clean']
];

// Mock Quill instance
class MockQuill {
  constructor(container, options = {}) {
    this.container = container;
    this.options = options;
    this.contents = { ops: [] };
    this.selection = { index: 0, length: 0 };
    this.text = '';
    this.length = 0;
    this.root = container;
    this.clipboard = {
      addMatcher: jest.fn(),
      dangerouslyPasteHTML: jest.fn()
    };
    this.keyboard = {
      addBinding: jest.fn()
    };
    this.history = {
      clear: jest.fn(),
      undo: jest.fn(),
      redo: jest.fn()
    };
    this.theme = options.theme || 'snow';
    this.modules = options.modules || {};
    this.formats = options.formats || [];
    this.bounds = options.bounds || document.body;
    this.debug = options.debug || false;
    this.placeholder = options.placeholder || '';
    this.readOnly = options.readOnly || false;
    this.scrollingContainer = options.scrollingContainer || null;
    this.strict = options.strict !== false;
    this.theme = options.theme || 'snow';

    // Mock event handlers
    this.handlers = {};

    // Initialize with empty content
    this.setContents([]);

    // Set up DOM structure
    this.setupDOM();
  }

  setupDOM() {
    // Create mock Quill DOM structure
    this.container.className = `ql-container ql-${this.theme}`;
    this.container.innerHTML = `
      <div class="ql-toolbar ql-${this.theme}" data-testid="quill-toolbar">
        <button class="ql-bold" data-testid="bold-button" type="button"></button>
        <button class="ql-italic" data-testid="italic-button" type="button"></button>
        <button class="ql-underline" type="button"></button>
        <button class="ql-strike" type="button"></button>
      </div>
      <div class="ql-editor" data-testid="quill-content" contenteditable="true"></div>
    `;

    this.toolbar = this.container.querySelector('.ql-toolbar');
    this.editor = this.container.querySelector('.ql-editor');

    // Add event listeners for toolbar buttons
    this.setupToolbarEvents();
  }

  setupToolbarEvents() {
    const buttons = this.container.querySelectorAll('.ql-toolbar button');
    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const format = button.className.replace('ql-', '');
        this.format(format, !this.getFormat()[format]);

        // Toggle active state
        button.classList.toggle('ql-active');

        // Trigger text change
        this.emitTextChange();
      });
    });
  }

  // Core Quill API methods
  deleteText(index, length) {
    const newText = this.text.slice(0, index) + this.text.slice(index + length);
    this.setText(newText);
    return this;
  }

  getContents(index, length) {
    return this.contents;
  }

  getLength() {
    return this.text.length + 1; // +1 for trailing newline
  }

  getText(index = 0, length = this.getLength()) {
    return this.text.slice(index, index + length);
  }

  insertEmbed(index, type, value) {
    // Mock embed insertion
    return this;
  }

  insertText(index, text, formats) {
    const newText = this.text.slice(0, index) + text + this.text.slice(index);
    this.setText(newText);
    this.emitTextChange();
    return this;
  }

  setContents(delta) {
    this.contents = Array.isArray(delta) ? { ops: delta } : delta;
    this.text = this.deltaToText(this.contents);
    this.updateEditor();
    return this;
  }

  setText(text) {
    this.text = text || '';
    this.contents = { ops: [{ insert: this.text + '\n' }] };
    this.updateEditor();
    this.emitTextChange();
    return this;
  }

  updateContents(delta) {
    // Mock delta update
    this.setContents(delta);
    return this;
  }

  // Formatting methods
  format(name, value) {
    // Mock formatting
    this.emitSelectionChange();
    return this;
  }

  formatLine(index, length, name, value) {
    // Mock line formatting
    return this;
  }

  formatText(index, length, name, value) {
    // Mock text formatting
    return this;
  }

  getFormat(index, length) {
    // Mock format retrieval
    return {};
  }

  removeFormat(index, length) {
    // Mock format removal
    return this;
  }

  // Selection methods
  getBounds(index, length) {
    return {
      bottom: 20,
      height: 20,
      left: 0,
      right: 100,
      top: 0,
      width: 100
    };
  }

  getSelection(focus) {
    return this.selection;
  }

  setSelection(index, length, source) {
    this.selection = { index: index || 0, length: length || 0 };
    this.emitSelectionChange();
    return this;
  }

  // Editor methods
  blur() {
    if (this.editor) {
      this.editor.blur();
    }
    this.emitSelectionChange();
  }

  focus() {
    if (this.editor) {
      this.editor.focus();
    }
    this.emitSelectionChange();
  }

  hasFocus() {
    return document.activeElement === this.editor;
  }

  update() {
    // Mock update
  }

  // Event methods
  on(eventName, handler) {
    if (!this.handlers[eventName]) {
      this.handlers[eventName] = [];
    }
    this.handlers[eventName].push(handler);
    return this;
  }

  off(eventName, handler) {
    if (this.handlers[eventName]) {
      const index = this.handlers[eventName].indexOf(handler);
      if (index > -1) {
        this.handlers[eventName].splice(index, 1);
      }
    }
    return this;
  }

  emit(eventName, ...args) {
    if (this.handlers[eventName]) {
      this.handlers[eventName].forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          console.error('Error in Quill event handler:', error);
        }
      });
    }
  }

  // Utility methods
  deltaToText(delta) {
    if (!delta || !delta.ops) return '';
    return delta.ops
      .filter(op => typeof op.insert === 'string')
      .map(op => op.insert)
      .join('')
      .replace(/\n$/, ''); // Remove trailing newline
  }

  updateEditor() {
    if (this.editor) {
      this.editor.innerHTML = this.text.replace(/\n/g, '<br>');
    }
  }

  emitTextChange() {
    this.emit('text-change', this.contents, {}, 'user');
  }

  emitSelectionChange() {
    this.emit('selection-change', this.selection, {}, 'user');
  }

  // Scroll methods
  scrollIntoView() {
    if (this.editor) {
      this.editor.scrollIntoView();
    }
  }

  // Module access
  getModule(name) {
    return this.modules[name] || null;
  }
}

// Mock ReactQuill component
const ReactQuill = forwardRef(({
  value = '',
  onChange = () => { },
  onChangeSelection = () => { },
  onFocus = () => { },
  onBlur = () => { },
  onKeyPress = () => { },
  onKeyDown = () => { },
  onKeyUp = () => { },
  placeholder = '',
  readOnly = false,
  theme = 'snow',
  modules = {},
  formats = [],
  bounds = null,
  children = null,
  style = {},
  className = '',
  id = '',
  tabIndex = null,
  preserveWhitespace = false,
  ...props
}, ref) => {
  const [quillInstance, setQuillInstance] = useState(null);
  const [currentValue, setCurrentValue] = useState(value);

  // Expose Quill instance through ref
  useImperativeHandle(ref, () => ({
    getEditor: () => quillInstance,
    getEditingArea: () => quillInstance?.editor,
    focus: () => quillInstance?.focus(),
    blur: () => quillInstance?.blur(),
    makeUnprivilegedEditor: () => quillInstance
  }), [quillInstance]);

  // Initialize Quill instance
  useEffect(() => {
    const container = document.createElement('div');
    container.setAttribute('data-testid', 'quill-editor');

    const quill = new MockQuill(container, {
      theme,
      modules,
      formats,
      bounds,
      placeholder,
      readOnly
    });

    // Set up event handlers
    quill.on('text-change', (delta, oldDelta, source) => {
      const newValue = quill.getText();
      setCurrentValue(newValue);
      if (onChange) {
        onChange(newValue, delta, source, quill);
      }
    });

    quill.on('selection-change', (range, oldRange, source) => {
      if (onChangeSelection) {
        onChangeSelection(range, source, quill);
      }
    });

    // Set initial value
    if (value !== undefined) {
      quill.setText(value);
    }

    setQuillInstance(quill);

    return () => {
      // Cleanup
      quill.handlers = {};
    };
  }, []);

  // Update value when prop changes
  useEffect(() => {
    if (quillInstance && value !== currentValue) {
      quillInstance.setText(value);
      setCurrentValue(value);
    }
  }, [value, quillInstance, currentValue]);

  // Update readOnly state
  useEffect(() => {
    if (quillInstance) {
      quillInstance.readOnly = readOnly;
      if (quillInstance.editor) {
        quillInstance.editor.contentEditable = !readOnly;
        if (readOnly) {
          quillInstance.editor.setAttribute('readOnly', 'true');
        } else {
          quillInstance.editor.removeAttribute('readOnly');
        }
      }
    }
  }, [readOnly, quillInstance]);

  return (
    <div
      className={`quill-wrapper ${className}`}
      style={style}
      id={id}
      tabIndex={tabIndex}
      data-testid="quill-editor"
      {...props}
    >
      {/* Render Quill structure directly for better test compatibility */}
      <div className={`ql-container ql-${theme}`}>
        <div className={`ql-toolbar ql-${theme}`} data-testid="quill-toolbar">
          <button className="ql-bold" data-testid="bold-button" type="button"></button>
          <button className="ql-italic" data-testid="italic-button" type="button"></button>
          <button className="ql-underline" type="button"></button>
          <button className="ql-strike" type="button"></button>
        </div>
        <div
          className="ql-editor"
          data-testid="quill-content"
          contentEditable={!readOnly}
          suppressContentEditableWarning={true}
          style={{ minHeight: typeof style?.height === 'number' ? `${style.height}px` : style?.height }}
        >
          {currentValue || placeholder}
        </div>
      </div>
      {children}
    </div>
  );
});

ReactQuill.displayName = 'ReactQuill';

// Export the mock
export default ReactQuill;

// Also export Quill for direct access
export const Quill = MockQuill;
