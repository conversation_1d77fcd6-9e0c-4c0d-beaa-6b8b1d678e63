FROM python:3.9-slim

ARG OPENAI_API_KEY
ARG AI_STUDIO_API_KEY
ARG STABILITY_API_KEY
ARG ELEVENLABS_API_KEY

ENV OPENAI_API_KEY=${OPENAI_API_KEY}
ENV AI_STUDIO_API_KEY=${AI_STUDIO_API_KEY}
ENV STABILITY_API_KEY=${STABILITY_API_KEY}
ENV ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/usr/src/app

# Set work directory
WORKDIR /usr/src/app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    postgresql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Copy project
COPY . .

# Create a non-root user
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /usr/src/app
USER app

# Expose port
EXPOSE 8000

# Run server
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
